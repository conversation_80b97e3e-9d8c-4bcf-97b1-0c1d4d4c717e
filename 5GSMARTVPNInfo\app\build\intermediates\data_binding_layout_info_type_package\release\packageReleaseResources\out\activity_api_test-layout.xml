<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_api_test" modulePackage="com.official.fivegfastvpn" filePath="app\src\main\res\layout\activity_api_test.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_api_test_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="146" endOffset="12"/></Target><Target id="@+id/testStatusBtn" view="Button"><Expressions/><location startLine="45" startOffset="8" endLine="52" endOffset="47"/></Target><Target id="@+id/testConfigBtn" view="Button"><Expressions/><location startLine="54" startOffset="8" endLine="61" endOffset="47"/></Target><Target id="@+id/testServersBtn" view="Button"><Expressions/><location startLine="63" startOffset="8" endLine="70" endOffset="47"/></Target><Target id="@+id/testCustomAdsBtn" view="Button"><Expressions/><location startLine="72" startOffset="8" endLine="79" endOffset="47"/></Target><Target id="@+id/testIpBtn" view="Button"><Expressions/><location startLine="81" startOffset="8" endLine="88" endOffset="47"/></Target><Target id="@+id/testTrackingBtn" view="Button"><Expressions/><location startLine="90" startOffset="8" endLine="97" endOffset="48"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="109" startOffset="8" endLine="121" endOffset="43"/></Target></Targets></Layout>