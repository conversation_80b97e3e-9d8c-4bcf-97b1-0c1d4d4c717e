plugins {
    alias(libs.plugins.androidApplication)
    alias(libs.plugins.jetbrainsKotlinAndroid)

    id 'io.sentry.android.gradle' version '5.1.0'
    id 'com.google.gms.google-services'
}

android {
    namespace 'com.official.fivegfastvpn'
    compileSdk 35

    defaultConfig {
        applicationId "com.official.fivegfastvpn"
        minSdk 23
        targetSdk 35
        versionCode 12
        versionName "12"
        multiDexEnabled true

        ndk {
            debugSymbolLevel 'FULL'
        }

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildFeatures {
        dataBinding true
        viewBinding true
    }
    dataBinding {
        enabled true
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        dex {
            useLegacyPackaging true
        }
        jniLibs {
            useLegacyPackaging true
        }
        exclude "jniLibs/**"
    }
    kotlinOptions {
        jvmTarget = '17'
    }

}

dependencies {

    implementation project(':nativetemplates')
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(path: ':vpnLib')
    implementation libs.core.ktx
    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.navigation.fragment
    implementation libs.navigation.ui
    implementation libs.androidx.core.ktx
    implementation libs.androidx.swiperefreshlayout

    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core

    implementation libs.material

    implementation libs.android.gif.drawable
    // Glide image loader
    implementation libs.glide
    annotationProcessor libs.compiler

    implementation libs.billing
    // Update
    implementation libs.app.update
    // Volley
    implementation libs.volley
    implementation libs.localbroadcastmanager
    implementation libs.work.runtime

    // Ads
    implementation libs.play.services.ads
    implementation libs.facebook
    implementation libs.audience.network.sdk
    implementation libs.infer.annotation
    implementation libs.user.messaging.platform

    //firebase
    implementation libs.firebase.messaging
    //lottie
    implementation libs.lottie
}

sentry {
    org = "mk-dev-p0"
    projectName = "smart-vpn"

    // this will upload your source code to Sentry to show it as part of the stack traces
    // disable if you don't want to expose your sources
    includeSourceContext = true
}
