package com.official.fivegfastvpn.utils;

import android.content.Context;
import android.content.Intent;
import android.net.VpnService;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import java.util.concurrent.TimeUnit;

import de.blinkt.openvpn.OpenVpnApi;
import de.blinkt.openvpn.core.VpnStatus;
import de.blinkt.openvpn.core.ConnectionStatus;

/**
 * Helper class to manage VPN connections with improved error handling and timeout management
 */
public class VpnServiceHelper {
    private static final String TAG = "VpnServiceHelper";
    private static final long CONNECTION_TIMEOUT = TimeUnit.SECONDS.toMillis(30); // 30 seconds timeout
    
    private Context context;
    private Handler timeoutHandler;
    private Runnable timeoutRunnable;
    private boolean isConnecting = false;
    
    public interface VpnConnectionCallback {
        void onConnectionSuccess();
        void onConnectionFailed(String error);
        void onConnectionTimeout();
    }
    
    public VpnServiceHelper(Context context) {
        this.context = context;
        this.timeoutHandler = new Handler(Looper.getMainLooper());
    }
    
    /**
     * Start VPN connection with timeout and error handling
     */
    public void startVpnWithTimeout(String config, String country, String username, String password, 
                                   VpnConnectionCallback callback) {
        if (isConnecting) {
            Log.w(TAG, "VPN connection already in progress");
            return;
        }
        
        isConnecting = true;
        Log.d(TAG, "Starting VPN connection to: " + country);
        
        // Set up timeout
        timeoutRunnable = () -> {
            Log.e(TAG, "VPN connection timeout after " + CONNECTION_TIMEOUT + "ms");
            isConnecting = false;
            if (callback != null) {
                callback.onConnectionTimeout();
            }
            // Send timeout state to UI
            sendConnectionState("TIMEOUT");
        };
        
        timeoutHandler.postDelayed(timeoutRunnable, CONNECTION_TIMEOUT);
        
        try {
            // Check VPN permission first
            Intent vpnIntent = VpnService.prepare(context);
            if (vpnIntent != null) {
                Log.e(TAG, "VPN permission not granted");
                cancelTimeout();
                isConnecting = false;
                if (callback != null) {
                    callback.onConnectionFailed("VPN permission not granted");
                }
                return;
            }
            
            // Start VPN connection
            OpenVpnApi.startVpn(context, config, country, username, password);
            
            // Monitor connection state
            VpnStatus.StateListener stateListener = new VpnStatus.StateListener() {
                @Override
                public void updateState(String state, String logmessage, int localizedResId,
                                      ConnectionStatus level, Intent intent) {
                    Log.d(TAG, "VPN State: " + state + ", Level: " + level + ", Message: " + logmessage);
                    
                    if (level == ConnectionStatus.LEVEL_CONNECTED) {
                        Log.i(TAG, "VPN connected successfully");
                        cancelTimeout();
                        isConnecting = false;
                        VpnStatus.removeStateListener(this);
                        if (callback != null) {
                            callback.onConnectionSuccess();
                        }
                    } else if (level == ConnectionStatus.LEVEL_AUTH_FAILED ||
                              level == ConnectionStatus.LEVEL_NOTCONNECTED) {
                        Log.e(TAG, "VPN connection failed: " + logmessage);
                        cancelTimeout();
                        isConnecting = false;
                        VpnStatus.removeStateListener(this);
                        if (callback != null) {
                            callback.onConnectionFailed(logmessage);
                        }
                    }
                    
                    // Send state to UI
                    sendConnectionState(state);
                }
                
                @Override
                public void setConnectedVPN(String uuid) {
                    Log.d(TAG, "Connected VPN UUID: " + uuid);
                }
            };
            
            VpnStatus.addStateListener(stateListener);
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting VPN: " + e.getMessage(), e);
            cancelTimeout();
            isConnecting = false;
            if (callback != null) {
                callback.onConnectionFailed("Error starting VPN: " + e.getMessage());
            }
        }
    }
    
    /**
     * Cancel the connection timeout
     */
    private void cancelTimeout() {
        if (timeoutRunnable != null) {
            timeoutHandler.removeCallbacks(timeoutRunnable);
            timeoutRunnable = null;
        }
    }
    
    /**
     * Send connection state to UI via broadcast
     */
    private void sendConnectionState(String state) {
        Intent intent = new Intent("connectionState");
        intent.putExtra("state", state);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }
    
    /**
     * Check if VPN is currently connecting
     */
    public boolean isConnecting() {
        return isConnecting;
    }
    
    /**
     * Reset connection state
     */
    public void reset() {
        cancelTimeout();
        isConnecting = false;
    }
    
    /**
     * Clean up resources
     */
    public void cleanup() {
        cancelTimeout();
        isConnecting = false;
    }
}
