# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# Keep VPN related classes
-keep class de.blinkt.openvpn.** { *; }
-keep class com.official.fivegfastvpn.** { *; }

# Keep OpenVPN native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep AIDL interfaces
-keep interface de.blinkt.openvpn.api.** { *; }

# Keep VPN Service and related classes
-keep class * extends android.net.VpnService { *; }
-keep class * implements android.os.Parcelable { *; }

# Keep notification related classes
-keep class * extends android.app.Service { *; }
-keep class * extends android.content.BroadcastReceiver { *; }

# Keep Firebase and Google Play Services
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Keep Sentry
-keep class io.sentry.** { *; }

# Preserve line numbers for debugging crashes
-keepattributes LineNumberTable,SourceFile
-renamesourcefileattribute SourceFile