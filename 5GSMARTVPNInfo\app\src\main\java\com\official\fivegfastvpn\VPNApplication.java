package com.official.fivegfastvpn;

import android.app.Activity;
import android.app.Application;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.os.Build;
import android.util.Log;

import com.official.fivegfastvpn.ads.AppOpenManager;
import com.google.firebase.FirebaseApp;
import com.google.firebase.messaging.FirebaseMessaging;

import de.blinkt.openvpn.core.VpnStatus;

public class VPNApplication extends Application {
    private static final String TAG = "VPNApplication";
    private static AppOpenManager appOpenManager;

    @Override
    public void onCreate() {
        super.onCreate();

        // Initialize Firebase first
        initializeFirebase();

        // Initialize notification channels
        createNotificationChannels();

        // Initialize VPN logging
        VpnStatus.initLogCache(getCacheDir());

        // Initialize app open manager
        appOpenManager = new AppOpenManager(this);

        Log.d(TAG, "VPNApplication initialized");
    }

    private void initializeFirebase() {
        try {
            // Initialize Firebase
            FirebaseApp.initializeApp(this);
            Log.d(TAG, "Firebase initialized successfully");

            // Subscribe to topics for notifications
            subscribeToFirebaseTopics();

            // Get FCM token for debugging
            getFCMToken();

        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize Firebase: " + e.getMessage(), e);
        }
    }

    private void subscribeToFirebaseTopics() {
        // Subscribe to 'all' topic for general notifications
        FirebaseMessaging.getInstance().subscribeToTopic("all")
            .addOnCompleteListener(task -> {
                String msg = "Subscribed to 'all' topic";
                if (!task.isSuccessful()) {
                    msg = "Failed to subscribe to 'all' topic";
                    Log.e(TAG, msg, task.getException());
                } else {
                    Log.d(TAG, msg);
                }
            });

        // Subscribe to 'premium' topic
        FirebaseMessaging.getInstance().subscribeToTopic("premium")
            .addOnCompleteListener(task -> {
                String msg = "Subscribed to 'premium' topic";
                if (!task.isSuccessful()) {
                    msg = "Failed to subscribe to 'premium' topic";
                    Log.e(TAG, msg, task.getException());
                } else {
                    Log.d(TAG, msg);
                }
            });

        // Subscribe to 'test' topic for testing
        FirebaseMessaging.getInstance().subscribeToTopic("test")
            .addOnCompleteListener(task -> {
                String msg = "Subscribed to 'test' topic";
                if (!task.isSuccessful()) {
                    msg = "Failed to subscribe to 'test' topic";
                    Log.e(TAG, msg, task.getException());
                } else {
                    Log.d(TAG, msg);
                }
            });
    }

    private void getFCMToken() {
        FirebaseMessaging.getInstance().getToken()
            .addOnCompleteListener(task -> {
                if (!task.isSuccessful()) {
                    Log.w(TAG, "Fetching FCM registration token failed", task.getException());
                    return;
                }

                // Get new FCM registration token
                String token = task.getResult();
                Log.d(TAG, "FCM Registration Token: " + token);

                // You can send this token to your server for targeted notifications
                // sendTokenToServer(token);
            });
    }

    private void createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = getSystemService(NotificationManager.class);

            // VPN Status Channel
            NotificationChannel vpnChannel = new NotificationChannel(
                "openvpn_bg",
                "VPN Status",
                NotificationManager.IMPORTANCE_LOW
            );
            vpnChannel.setDescription("Shows VPN connection status");
            vpnChannel.setShowBadge(false);
            notificationManager.createNotificationChannel(vpnChannel);

            // VPN New Status Channel
            NotificationChannel vpnNewStatusChannel = new NotificationChannel(
                "openvpn_newstatus",
                "VPN Connection Updates",
                NotificationManager.IMPORTANCE_DEFAULT
            );
            vpnNewStatusChannel.setDescription("Shows VPN connection updates");
            notificationManager.createNotificationChannel(vpnNewStatusChannel);

            // Firebase Notification Channels
            NotificationChannel generalChannel = new NotificationChannel(
                "general_notifications",
                "General Notifications",
                NotificationManager.IMPORTANCE_DEFAULT
            );
            generalChannel.setDescription("General app notifications");
            generalChannel.setShowBadge(true);
            notificationManager.createNotificationChannel(generalChannel);

            NotificationChannel importantChannel = new NotificationChannel(
                "important_notifications",
                "Important Notifications",
                NotificationManager.IMPORTANCE_HIGH
            );
            importantChannel.setDescription("Important app notifications");
            importantChannel.setShowBadge(true);
            notificationManager.createNotificationChannel(importantChannel);

            NotificationChannel promotionalChannel = new NotificationChannel(
                "promotional_notifications",
                "Promotional Notifications",
                NotificationManager.IMPORTANCE_LOW
            );
            promotionalChannel.setDescription("Promotional and marketing notifications");
            promotionalChannel.setShowBadge(false);
            notificationManager.createNotificationChannel(promotionalChannel);

            Log.d(TAG, "All notification channels created");
        }
    }

    public void initializeAppOpenAd() {
        if (appOpenManager != null) {
            appOpenManager.initializeAd();
        }
    }

    public void showAdIfAvailable(Activity activity) {
        if (appOpenManager != null) {
            appOpenManager.showAdFromSplash(activity);
        }
    }

    public void setAdLoadCallback(AppOpenManager.OnAppOpenAdLoadCallback callback) {
        if (appOpenManager != null) {
            appOpenManager.setAdLoadCallback(callback);
        }
    }
}
