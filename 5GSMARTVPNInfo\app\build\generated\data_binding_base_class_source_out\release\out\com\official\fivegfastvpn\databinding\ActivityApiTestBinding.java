// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityApiTestBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final TextView statusText;

  @NonNull
  public final Button testConfigBtn;

  @NonNull
  public final Button testCustomAdsBtn;

  @NonNull
  public final Button testIpBtn;

  @NonNull
  public final Button testServersBtn;

  @NonNull
  public final Button testStatusBtn;

  @NonNull
  public final Button testTrackingBtn;

  private ActivityApiTestBinding(@NonNull ScrollView rootView, @NonNull TextView statusText,
      @NonNull Button testConfigBtn, @NonNull Button testCustomAdsBtn, @NonNull Button testIpBtn,
      @NonNull Button testServersBtn, @NonNull Button testStatusBtn,
      @NonNull Button testTrackingBtn) {
    this.rootView = rootView;
    this.statusText = statusText;
    this.testConfigBtn = testConfigBtn;
    this.testCustomAdsBtn = testCustomAdsBtn;
    this.testIpBtn = testIpBtn;
    this.testServersBtn = testServersBtn;
    this.testStatusBtn = testStatusBtn;
    this.testTrackingBtn = testTrackingBtn;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityApiTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityApiTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_api_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityApiTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      id = R.id.testConfigBtn;
      Button testConfigBtn = ViewBindings.findChildViewById(rootView, id);
      if (testConfigBtn == null) {
        break missingId;
      }

      id = R.id.testCustomAdsBtn;
      Button testCustomAdsBtn = ViewBindings.findChildViewById(rootView, id);
      if (testCustomAdsBtn == null) {
        break missingId;
      }

      id = R.id.testIpBtn;
      Button testIpBtn = ViewBindings.findChildViewById(rootView, id);
      if (testIpBtn == null) {
        break missingId;
      }

      id = R.id.testServersBtn;
      Button testServersBtn = ViewBindings.findChildViewById(rootView, id);
      if (testServersBtn == null) {
        break missingId;
      }

      id = R.id.testStatusBtn;
      Button testStatusBtn = ViewBindings.findChildViewById(rootView, id);
      if (testStatusBtn == null) {
        break missingId;
      }

      id = R.id.testTrackingBtn;
      Button testTrackingBtn = ViewBindings.findChildViewById(rootView, id);
      if (testTrackingBtn == null) {
        break missingId;
      }

      return new ActivityApiTestBinding((ScrollView) rootView, statusText, testConfigBtn,
          testCustomAdsBtn, testIpBtn, testServersBtn, testStatusBtn, testTrackingBtn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
