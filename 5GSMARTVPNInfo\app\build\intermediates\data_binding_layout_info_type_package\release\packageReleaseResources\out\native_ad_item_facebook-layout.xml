<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="native_ad_item_facebook" modulePackage="com.official.fivegfastvpn" filePath="app\src\main\res\layout\native_ad_item_facebook.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/native_ad_item_facebook_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="112" endOffset="14"/></Target><Target id="@+id/native_ad_icon" view="com.facebook.ads.MediaView"><Expressions/><location startLine="16" startOffset="8" endLine="19" endOffset="42"/></Target><Target id="@+id/native_ad_title" view="TextView"><Expressions/><location startLine="28" startOffset="12" endLine="35" endOffset="41"/></Target><Target id="@+id/native_ad_sponsored_label" view="TextView"><Expressions/><location startLine="37" startOffset="12" endLine="44" endOffset="41"/></Target><Target id="@+id/ad_choices_container" view="LinearLayout"><Expressions/><location startLine="48" startOffset="8" endLine="53" endOffset="46"/></Target><Target id="@+id/native_ad_media" view="com.facebook.ads.MediaView"><Expressions/><location startLine="57" startOffset="4" endLine="61" endOffset="34"/></Target><Target id="@+id/native_ad_social_context" view="TextView"><Expressions/><location startLine="75" startOffset="12" endLine="82" endOffset="41"/></Target><Target id="@+id/native_ad_body" view="TextView"><Expressions/><location startLine="84" startOffset="12" endLine="92" endOffset="41"/></Target><Target id="@+id/native_ad_call_to_action" view="Button"><Expressions/><location startLine="96" startOffset="8" endLine="108" endOffset="13"/></Target></Targets></Layout>