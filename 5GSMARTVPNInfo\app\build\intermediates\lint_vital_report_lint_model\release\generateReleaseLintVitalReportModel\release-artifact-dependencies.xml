<dependencies>
  <compile
      roots="androidx.databinding:databinding-runtime:8.10.1@aar,androidx.databinding:viewbinding:8.10.1@aar,androidx.databinding:databinding-adapters:8.10.1@aar,androidx.databinding:databinding-common:8.10.1@jar,androidx.databinding:databinding-ktx:8.10.1@aar,androidx.navigation:navigation-runtime-android:2.9.0@aar,androidx.navigation:navigation-common-android:2.9.0@aar,androidx.navigation:navigation-fragment:2.9.0@aar,androidx.navigation:navigation-ui:2.9.0@aar,com.google.android.material:material:1.12.0@aar,com.airbnb.android:lottie:6.6.1@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.android.billingclient:billing:7.1.1@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment-ktx:1.6.2@aar,com.google.android.gms:play-services-location:19.0.0@aar,com.google.ads.mediation:facebook:6.20.0.0@aar,com.google.android.gms:play-services-ads:24.3.0@aar,com.google.android.gms:play-services-appset:16.0.1@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.android.play:app-update:2.1.0@aar,com.facebook.android:audience-network-sdk:6.20.0@aar,com.google.android.gms:play-services-ads-api:24.3.0@aar,com.google.android.ump:user-messaging-platform:3.2.0@aar,com.google.firebase:firebase-messaging:24.1.1@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.firebase:firebase-installations:17.2.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,androidx.work:work-runtime:2.10.1@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar,androidx.lifecycle:lifecycle-service:2.9.0@aar,androidx.lifecycle:lifecycle-process:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.transition:transition:1.5.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.browser:browser:1.8.0@aar,androidx.webkit:webkit:1.11.0-alpha02@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-places-placereport:17.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar,com.google.android.gms:play-services-measurement-base:20.1.2@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.activity:activity:1.10.1@aar,androidx.activity:activity:1.10.1@aar,androidx.core:core-ktx:1.16.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.core:core-viewtree:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.android.datatransport:transport-api:3.1.0@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.4.2@jar,androidx.collection:collection-jvm:1.4.2@jar,androidx.arch.core:core-common:2.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.10@jar,:@@:nativetemplates::release,:@@:vpnLib::release,androidx.constraintlayout:constraintlayout:2.2.1@aar,pl.droidsonroids.gif:android-gif-drawable:1.2.28@aar,com.android.volley:volley:1.2.1@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,io.sentry:sentry-android:8.1.0@aar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.tracing:tracing:1.2.0@aar,org.jspecify:jspecify:1.0.0@jar,androidx.startup:startup-runtime:1.1.1@aar,io.sentry:sentry-android-fragment:8.1.0@aar,io.sentry:sentry-android-ndk:8.1.0@aar,io.sentry:sentry-android-core:8.1.0@aar,io.sentry:sentry-android-replay:8.1.0@aar,io.sentry:sentry-kotlin-extensions:8.1.0@jar,io.sentry:sentry:8.1.0@jar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,androidx.exifinterface:exifinterface:1.3.6@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.google.android.play:core-common:2.0.3@aar,org.jacoco:org.jacoco.core:0.8.8@jar,org.ow2.asm:asm-commons:9.2@jar,org.ow2.asm:asm-analysis:9.2@jar,org.ow2.asm:asm-tree:9.2@jar,org.ow2.asm:asm:9.2@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar">
    <dependency
        name="androidx.databinding:databinding-runtime:8.10.1@aar"
        simpleName="androidx.databinding:databinding-runtime"/>
    <dependency
        name="androidx.databinding:viewbinding:8.10.1@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.databinding:databinding-adapters:8.10.1@aar"
        simpleName="androidx.databinding:databinding-adapters"/>
    <dependency
        name="androidx.databinding:databinding-common:8.10.1@jar"
        simpleName="androidx.databinding:databinding-common"/>
    <dependency
        name="androidx.databinding:databinding-ktx:8.10.1@aar"
        simpleName="androidx.databinding:databinding-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-runtime-android"/>
    <dependency
        name="androidx.navigation:navigation-common-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-common-android"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.9.0@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.9.0@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="com.airbnb.android:lottie:6.6.1@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.android.billingclient:billing:7.1.1@aar"
        simpleName="com.android.billingclient:billing"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.2@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="com.google.android.gms:play-services-location:19.0.0@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.ads.mediation:facebook:6.20.0.0@aar"
        simpleName="com.google.ads.mediation:facebook"/>
    <dependency
        name="com.google.android.gms:play-services-ads:24.3.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.android.gms:play-services-appset:16.0.1@aar"
        simpleName="com.google.android.gms:play-services-appset"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.play:app-update:2.1.0@aar"
        simpleName="com.google.android.play:app-update"/>
    <dependency
        name="com.facebook.android:audience-network-sdk:6.20.0@aar"
        simpleName="com.facebook.android:audience-network-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-ads-api:24.3.0@aar"
        simpleName="com.google.android.gms:play-services-ads-api"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.1.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.firebase:firebase-installations:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="androidx.work:work-runtime:2.10.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.webkit:webkit:1.11.0-alpha02@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-places-placereport:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-places-placereport"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.2@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.2@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name=":@@:nativetemplates::release"
        simpleName="artifacts::nativetemplates"/>
    <dependency
        name=":@@:vpnLib::release"
        simpleName="artifacts::vpnLib"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="pl.droidsonroids.gif:android-gif-drawable:1.2.28@aar"
        simpleName="pl.droidsonroids.gif:android-gif-drawable"/>
    <dependency
        name="com.android.volley:volley:1.2.1@aar"
        simpleName="com.android.volley:volley"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="io.sentry:sentry-android:8.1.0@aar"
        simpleName="io.sentry:sentry-android"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="io.sentry:sentry-android-fragment:8.1.0@aar"
        simpleName="io.sentry:sentry-android-fragment"/>
    <dependency
        name="io.sentry:sentry-android-ndk:8.1.0@aar"
        simpleName="io.sentry:sentry-android-ndk"/>
    <dependency
        name="io.sentry:sentry-android-core:8.1.0@aar"
        simpleName="io.sentry:sentry-android-core"/>
    <dependency
        name="io.sentry:sentry-android-replay:8.1.0@aar"
        simpleName="io.sentry:sentry-android-replay"/>
    <dependency
        name="io.sentry:sentry-kotlin-extensions:8.1.0@jar"
        simpleName="io.sentry:sentry-kotlin-extensions"/>
    <dependency
        name="io.sentry:sentry:8.1.0@jar"
        simpleName="io.sentry:sentry"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.android.play:core-common:2.0.3@aar"
        simpleName="com.google.android.play:core-common"/>
    <dependency
        name="org.jacoco:org.jacoco.core:0.8.8@jar"
        simpleName="org.jacoco:org.jacoco.core"/>
    <dependency
        name="org.ow2.asm:asm-commons:9.2@jar"
        simpleName="org.ow2.asm:asm-commons"/>
    <dependency
        name="org.ow2.asm:asm-analysis:9.2@jar"
        simpleName="org.ow2.asm:asm-analysis"/>
    <dependency
        name="org.ow2.asm:asm-tree:9.2@jar"
        simpleName="org.ow2.asm:asm-tree"/>
    <dependency
        name="org.ow2.asm:asm:9.2@jar"
        simpleName="org.ow2.asm:asm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
  </compile>
  <package
      roots="androidx.databinding:databinding-adapters:8.10.1@aar,androidx.databinding:databinding-ktx:8.10.1@aar,androidx.databinding:databinding-runtime:8.10.1@aar,androidx.databinding:viewbinding:8.10.1@aar,androidx.databinding:databinding-common:8.10.1@jar,:@@:nativetemplates::release,:@@:vpnLib::release,androidx.navigation:navigation-runtime-android:2.9.0@aar,androidx.navigation:navigation-common-android:2.9.0@aar,androidx.navigation:navigation-fragment:2.9.0@aar,androidx.navigation:navigation-ui:2.9.0@aar,com.google.android.material:material:1.12.0@aar,androidx.constraintlayout:constraintlayout:2.2.1@aar,com.airbnb.android:lottie:6.6.1@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.android.billingclient:billing:7.1.1@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment-ktx:1.6.2@aar,com.google.firebase:firebase-messaging:24.1.1@aar,com.google.android.gms:play-services-location:19.0.0@aar,com.google.ads.mediation:facebook:6.20.0.0@aar,com.google.android.gms:play-services-ads:24.3.0@aar,com.google.android.gms:play-services-appset:16.0.1@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.android.play:app-update:2.1.0@aar,com.facebook.android:audience-network-sdk:6.20.0@aar,com.google.android.gms:play-services-ads-api:24.3.0@aar,com.google.android.ump:user-messaging-platform:3.2.0@aar,com.google.firebase:firebase-installations:17.2.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,androidx.work:work-runtime:2.10.1@aar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-ktx:2.6.1@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.window:window:1.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.transition:transition:1.5.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.browser:browser:1.8.0@aar,androidx.webkit:webkit:1.11.0-alpha02@aar,io.sentry:sentry-android:8.1.0@aar,io.sentry:sentry-android-ndk:8.1.0@aar,io.sentry:sentry-android-core:8.1.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-service:2.9.0@aar,androidx.lifecycle:lifecycle-process:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-common-java8:2.9.0@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar,androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.concurrent:concurrent-futures-ktx:1.1.0@jar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-places-placereport:17.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar,com.google.android.gms:play-services-measurement-base:20.1.2@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.activity:activity:1.10.1@aar,androidx.activity:activity:1.10.1@aar,androidx.core:core-ktx:1.16.0@aar,io.sentry:sentry-android-replay:8.1.0@aar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,io.sentry:sentry-android-sqlite:8.1.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0@jar,androidx.core:core-viewtree:1.0.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.tracing:tracing-ktx:1.2.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.4.2@jar,androidx.collection:collection-jvm:1.4.2@jar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cardview:cardview:1.0.0@aar,androidx.constraintlayout:constraintlayout-core:1.1.1@jar,androidx.interpolator:interpolator:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.exifinterface:exifinterface:1.3.6@aar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.android.datatransport:transport-api:3.1.0@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.10@jar,pl.droidsonroids.gif:android-gif-drawable:1.2.28@aar,com.android.volley:volley:1.2.1@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:guava:31.1-android@jar,com.google.errorprone:error_prone_annotations:2.26.0@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,com.google.android.play:core-common:2.0.3@aar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,org.jacoco:org.jacoco.core:0.8.8@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.squareup.okio:okio:1.17.6@jar,org.jspecify:jspecify:1.0.0@jar,io.sentry:sentry-android-fragment:8.1.0@aar,io.sentry:sentry-kotlin-extensions:8.1.0@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,org.ow2.asm:asm-commons:9.2@jar,org.ow2.asm:asm-analysis:9.2@jar,org.ow2.asm:asm-tree:9.2@jar,org.ow2.asm:asm:9.2@jar,io.sentry:sentry:8.1.0@jar,io.sentry:sentry-native-ndk:0.7.19@aar,com.google.guava:failureaccess:1.0.1@jar,org.checkerframework:checker-qual:3.12.0@jar,com.google.j2objc:j2objc-annotations:1.3@jar">
    <dependency
        name="androidx.databinding:databinding-adapters:8.10.1@aar"
        simpleName="androidx.databinding:databinding-adapters"/>
    <dependency
        name="androidx.databinding:databinding-ktx:8.10.1@aar"
        simpleName="androidx.databinding:databinding-ktx"/>
    <dependency
        name="androidx.databinding:databinding-runtime:8.10.1@aar"
        simpleName="androidx.databinding:databinding-runtime"/>
    <dependency
        name="androidx.databinding:viewbinding:8.10.1@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.databinding:databinding-common:8.10.1@jar"
        simpleName="androidx.databinding:databinding-common"/>
    <dependency
        name=":@@:nativetemplates::release"
        simpleName="artifacts::nativetemplates"/>
    <dependency
        name=":@@:vpnLib::release"
        simpleName="artifacts::vpnLib"/>
    <dependency
        name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-runtime-android"/>
    <dependency
        name="androidx.navigation:navigation-common-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-common-android"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.9.0@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.9.0@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.airbnb.android:lottie:6.6.1@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.android.billingclient:billing:7.1.1@aar"
        simpleName="com.android.billingclient:billing"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.2@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.1.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-location:19.0.0@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.ads.mediation:facebook:6.20.0.0@aar"
        simpleName="com.google.ads.mediation:facebook"/>
    <dependency
        name="com.google.android.gms:play-services-ads:24.3.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.android.gms:play-services-appset:16.0.1@aar"
        simpleName="com.google.android.gms:play-services-appset"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.play:app-update:2.1.0@aar"
        simpleName="com.google.android.play:app-update"/>
    <dependency
        name="com.facebook.android:audience-network-sdk:6.20.0@aar"
        simpleName="com.facebook.android:audience-network-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-ads-api:24.3.0@aar"
        simpleName="com.google.android.gms:play-services-ads-api"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.firebase:firebase-installations:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="androidx.work:work-runtime:2.10.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.window:window:1.0.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.webkit:webkit:1.11.0-alpha02@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="io.sentry:sentry-android:8.1.0@aar"
        simpleName="io.sentry:sentry-android"/>
    <dependency
        name="io.sentry:sentry-android-ndk:8.1.0@aar"
        simpleName="io.sentry:sentry-android-ndk"/>
    <dependency
        name="io.sentry:sentry-android-core:8.1.0@aar"
        simpleName="io.sentry:sentry-android-core"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures-ktx"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-places-placereport:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-places-placereport"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:20.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.2@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="io.sentry:sentry-android-replay:8.1.0@aar"
        simpleName="io.sentry:sentry-android-replay"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="io.sentry:sentry-android-sqlite:8.1.0@aar"
        simpleName="io.sentry:sentry-android-sqlite"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.2@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.1.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="pl.droidsonroids.gif:android-gif-drawable:1.2.28@aar"
        simpleName="pl.droidsonroids.gif:android-gif-drawable"/>
    <dependency
        name="com.android.volley:volley:1.2.1@aar"
        simpleName="com.android.volley:volley"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:guava:31.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="com.google.android.play:core-common:2.0.3@aar"
        simpleName="com.google.android.play:core-common"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.jacoco:org.jacoco.core:0.8.8@jar"
        simpleName="org.jacoco:org.jacoco.core"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.squareup.okio:okio:1.17.6@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="io.sentry:sentry-android-fragment:8.1.0@aar"
        simpleName="io.sentry:sentry-android-fragment"/>
    <dependency
        name="io.sentry:sentry-kotlin-extensions:8.1.0@jar"
        simpleName="io.sentry:sentry-kotlin-extensions"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.ow2.asm:asm-commons:9.2@jar"
        simpleName="org.ow2.asm:asm-commons"/>
    <dependency
        name="org.ow2.asm:asm-analysis:9.2@jar"
        simpleName="org.ow2.asm:asm-analysis"/>
    <dependency
        name="org.ow2.asm:asm-tree:9.2@jar"
        simpleName="org.ow2.asm:asm-tree"/>
    <dependency
        name="org.ow2.asm:asm:9.2@jar"
        simpleName="org.ow2.asm:asm"/>
    <dependency
        name="io.sentry:sentry:8.1.0@jar"
        simpleName="io.sentry:sentry"/>
    <dependency
        name="io.sentry:sentry-native-ndk:0.7.19@aar"
        simpleName="io.sentry:sentry-native-ndk"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="org.checkerframework:checker-qual:3.12.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:1.3@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </package>
</dependencies>
