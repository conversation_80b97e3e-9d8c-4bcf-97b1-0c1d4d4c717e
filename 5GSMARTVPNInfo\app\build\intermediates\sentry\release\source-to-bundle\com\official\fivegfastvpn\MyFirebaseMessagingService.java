package com.official.fivegfastvpn;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.official.fivegfastvpn.model.NotificationModel;
import com.official.fivegfastvpn.utils.NotificationStorage;

import java.util.Map;

public class MyFirebaseMessagingService extends FirebaseMessagingService {

    private static final String TAG = "MyFirebaseMsgService";

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        Log.d(TAG, "Firebase message received from: " + remoteMessage.getFrom());
        Log.d(TAG, "Message ID: " + remoteMessage.getMessageId());
        Log.d(TAG, "Message Type: " + remoteMessage.getMessageType());
        Log.d(TAG, "Data payload: " + remoteMessage.getData());

        // Check if message contains a notification payload.
        if (remoteMessage.getNotification() != null) {
            String notificationBody = remoteMessage.getNotification().getBody();
            String notificationTitle = remoteMessage.getNotification().getTitle();

            // Log notification details.
            Log.d(TAG, "Notification Title: " + notificationTitle);
            Log.d(TAG, "Notification Body: " + notificationBody);

            // Handle notification with enhanced features
            handleNotification(notificationTitle, notificationBody, remoteMessage.getData());
        } else {
            Log.d(TAG, "Message has no notification payload");

            // Handle data-only messages
            if (!remoteMessage.getData().isEmpty()) {
                Log.d(TAG, "Handling data-only message");
                handleDataMessage(remoteMessage.getData());
            }
        }

        // Check if message contains a data payload.
        if (remoteMessage.getData().size() > 0) {
            Log.d(TAG, "Data Payload: " + remoteMessage.getData());

            // Handle data-only messages
            Map<String, String> data = remoteMessage.getData();
            if (data.containsKey("title") && data.containsKey("message")) {
                handleNotification(data.get("title"), data.get("message"), data);
            }
        }
    }

    /**
     * Handle incoming notification with enhanced features
     */
    private void handleNotification(String title, String body, Map<String, String> data) {
        // Create notification model for local storage
        NotificationModel notification = createNotificationFromData(title, body, data);

        // Save to local storage
        NotificationStorage storage = NotificationStorage.getInstance(this);
        storage.addNotification(notification);

        // Show system notification
        sendNotification(title, body, data);
    }

    /**
     * Create NotificationModel from FCM data
     */
    private NotificationModel createNotificationFromData(String title, String body, Map<String, String> data) {
        NotificationModel notification = new NotificationModel();

        // Generate a unique ID based on timestamp and title hash
        int id = (int) (System.currentTimeMillis() / 1000) + title.hashCode();
        notification.setId(id);
        notification.setTitle(title);
        notification.setMessage(body);
        notification.setStatus("received");
        notification.setCreatedAt(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                .format(new java.util.Date()));

        // Extract additional data if available
        if (data != null) {
            notification.setPriority(data.getOrDefault("priority", "normal"));
            notification.setCategory(data.getOrDefault("category", "general"));
            notification.setTargetAudience(data.getOrDefault("target_audience", "all_users"));
            notification.setNotificationType(data.getOrDefault("notification_type", "general"));

            // Store raw data as JSON string
            try {
                org.json.JSONObject jsonData = new org.json.JSONObject();
                for (Map.Entry<String, String> entry : data.entrySet()) {
                    jsonData.put(entry.getKey(), entry.getValue());
                }
                notification.setData(jsonData.toString());
            } catch (Exception e) {
                Log.e(TAG, "Error creating JSON data", e);
            }
        }

        return notification;
    }

    private void sendNotification(String title, String body) {
        sendNotification(title, body, null);
    }

    /**
     * Enhanced notification method with data support
     */
    private void sendNotification(String title, String body, Map<String, String> data) {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

        // Add notification data to intent if available
        if (data != null) {
            for (Map.Entry<String, String> entry : data.entrySet()) {
                intent.putExtra(entry.getKey(), entry.getValue());
            }
            intent.putExtra("from_notification", true);
        }

        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE);

        // Determine notification channel and priority based on data
        String channelId = getNotificationChannelId(data);
        int priority = getNotificationPriority(data);
        int color = getNotificationColor(data);

        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, channelId)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle(title)
                .setContentText(body)
                .setAutoCancel(true)
                .setSound(defaultSoundUri)
                .setContentIntent(pendingIntent)
                .setPriority(priority)
                .setColor(color)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(body));

        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        // Create notification channels for Android Oreo and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            createNotificationChannels(notificationManager);
        }

        // Use notification ID based on content to avoid duplicates
        int notificationId = (title + body).hashCode();
        notificationManager.notify(notificationId, notificationBuilder.build());
    }

    /**
     * Get notification channel ID based on priority/category
     */
    private String getNotificationChannelId(Map<String, String> data) {
        if (data == null) return "fcm_default_channel";

        String priority = data.getOrDefault("priority", "normal");
        String category = data.getOrDefault("category", "general");

        if ("urgent".equals(priority) || "security".equals(category)) {
            return "fcm_urgent_channel";
        } else if ("high".equals(priority) || "update".equals(category)) {
            return "fcm_high_channel";
        } else {
            return "fcm_default_channel";
        }
    }

    /**
     * Get notification priority
     */
    private int getNotificationPriority(Map<String, String> data) {
        if (data == null) return NotificationCompat.PRIORITY_DEFAULT;

        String priority = data.getOrDefault("priority", "normal");
        switch (priority) {
            case "urgent":
                return NotificationCompat.PRIORITY_MAX;
            case "high":
                return NotificationCompat.PRIORITY_HIGH;
            case "low":
                return NotificationCompat.PRIORITY_LOW;
            default:
                return NotificationCompat.PRIORITY_DEFAULT;
        }
    }

    /**
     * Get notification color based on category
     */
    private int getNotificationColor(Map<String, String> data) {
        if (data == null) return Color.parseColor("#3B82F6");

        String category = data.getOrDefault("category", "general");
        switch (category) {
            case "urgent":
            case "security":
                return Color.parseColor("#EF4444");
            case "update":
                return Color.parseColor("#3B82F6");
            case "maintenance":
                return Color.parseColor("#F59E0B");
            case "welcome":
                return Color.parseColor("#10B981");
            default:
                return Color.parseColor("#6366F1");
        }
    }

    /**
     * Create notification channels for different priorities
     */
    private void createNotificationChannels(NotificationManager notificationManager) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Default channel
            NotificationChannel defaultChannel = new NotificationChannel(
                    "fcm_default_channel",
                    "General Notifications",
                    NotificationManager.IMPORTANCE_DEFAULT
            );
            defaultChannel.setDescription("General app notifications");
            notificationManager.createNotificationChannel(defaultChannel);

            // High priority channel
            NotificationChannel highChannel = new NotificationChannel(
                    "fcm_high_channel",
                    "Important Notifications",
                    NotificationManager.IMPORTANCE_HIGH
            );
            highChannel.setDescription("Important app notifications");
            highChannel.enableLights(true);
            highChannel.setLightColor(Color.BLUE);
            notificationManager.createNotificationChannel(highChannel);

            // Urgent channel
            NotificationChannel urgentChannel = new NotificationChannel(
                    "fcm_urgent_channel",
                    "Urgent Notifications",
                    NotificationManager.IMPORTANCE_HIGH
            );
            urgentChannel.setDescription("Urgent security and system notifications");
            urgentChannel.enableLights(true);
            urgentChannel.setLightColor(Color.RED);
            urgentChannel.enableVibration(true);
            urgentChannel.setVibrationPattern(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});
            notificationManager.createNotificationChannel(urgentChannel);
        }
    }

    /**
     * Handle data-only messages
     */
    private void handleDataMessage(Map<String, String> data) {
        Log.d(TAG, "Processing data message: " + data);

        // Extract title and body from data if available
        String title = data.get("title");
        String body = data.get("body");

        if (title != null && body != null) {
            handleNotification(title, body, data);
        } else {
            Log.d(TAG, "Data message missing title or body");
        }
    }

    @Override
    public void onNewToken(String token) {
        Log.d(TAG, "FCM token refreshed: " + token);
        Log.d(TAG, "Token length: " + token.length());

        // Send token to server if needed
        sendRegistrationToServer(token);

        // Re-subscribe to topics with new token
        resubscribeToTopics();
    }

    /**
     * Re-subscribe to topics when token is refreshed
     */
    private void resubscribeToTopics() {
        Log.d(TAG, "Re-subscribing to Firebase topics");

        // This will be handled by the Application class
        // but we can add backup subscription here if needed
    }

    /**
     * Send FCM token to server for targeting
     */
    private void sendRegistrationToServer(String token) {
        // TODO: Implement token registration with your server
        // This can be used to send targeted notifications to specific devices
        Log.d(TAG, "Token ready to send to server: " + token);

        // You can implement API call here to send token to your admin panel
        // Example:
        // ApiService.sendFCMToken(token);
    }
}
