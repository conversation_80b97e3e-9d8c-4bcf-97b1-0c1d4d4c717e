{"logs": [{"outputFile": "com.official.fivegfastvpn.app-mergeReleaseResources-76:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\259d0aa544e023aed46b172a705803dc\\transformed\\appcompat-1.7.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2903"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,533,641,727,835,954,1038,1119,1210,1303,1399,1493,1593,1686,1781,1877,1968,2059,2146,2252,2358,2459,2566,2678,2782,2938,14153", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "424,528,636,722,830,949,1033,1114,1205,1298,1394,1488,1588,1681,1776,1872,1963,2054,2141,2247,2353,2454,2561,2673,2777,2933,3031,14236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a056806aa4aaa5631c39921f8bed3647\\transformed\\navigation-ui-2.9.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,127", "endOffsets": "155,283"}, "to": {"startLines": "136,137", "startColumns": "4,4", "startOffsets": "12392,12497", "endColumns": "104,127", "endOffsets": "12492,12620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4174daf14328b785510b24290d95f19a\\transformed\\play-services-ads-24.3.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,248,295,371,436,507,610,673,789,895,1018,1072,1128,1237,1336,1382,1483,1518,1551,1606,1693,1742", "endColumns": "48,46,75,64,70,102,62,115,105,122,53,55,108,98,45,100,34,32,54,86,48,55", "endOffsets": "247,294,370,435,506,609,672,788,894,1017,1071,1127,1236,1335,1381,1482,1517,1550,1605,1692,1741,1797"}, "to": {"startLines": "133,134,135,138,139,140,141,142,143,144,145,146,147,148,150,151,152,153,154,155,156,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12208,12261,12312,12625,12694,12769,12876,12943,13063,13173,13300,13358,13418,13531,13719,13769,13874,13913,13950,14009,14100,14594", "endColumns": "52,50,79,68,74,106,66,119,109,126,57,59,112,102,49,104,38,36,58,90,52,59", "endOffsets": "12256,12307,12387,12689,12764,12871,12938,13058,13168,13295,13353,13413,13526,13629,13764,13869,13908,13945,14004,14095,14148,14649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1aae7574d5734885cc339c3642ba94ef\\transformed\\play-services-basement-18.4.0\\res\\values-mk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "132", "endOffsets": "327"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5510", "endColumns": "136", "endOffsets": "5642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b1798530a3a51669983781b25eadf6b2\\transformed\\browser-1.8.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,273,388", "endColumns": "112,104,114,100", "endOffsets": "163,268,383,484"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6734,7067,7172,7287", "endColumns": "112,104,114,100", "endOffsets": "6842,7167,7282,7383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e1c57ccd2dc8ee4086206221e4fbff22\\transformed\\material-1.12.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,611,700,796,920,1007,1070,1136,1227,1297,1361,1464,1527,1592,1652,1720,1783,1838,1966,2023,2085,2140,2215,2355,2442,2521,2614,2700,2783,2916,2998,3083,3229,3316,3393,3447,3502,3568,3641,3717,3788,3866,3939,4015,4090,4160,4269,4357,4432,4524,4616,4690,4764,4856,4909,4991,5058,5141,5228,5290,5354,5417,5487,5601,5716,5818,5930,5988,6047,6132,6221,6305", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,96,88,95,123,86,62,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,78,92,85,82,132,81,84,145,86,76,53,54,65,72,75,70,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84,88,83,78", "endOffsets": "266,347,427,509,606,695,791,915,1002,1065,1131,1222,1292,1356,1459,1522,1587,1647,1715,1778,1833,1961,2018,2080,2135,2210,2350,2437,2516,2609,2695,2778,2911,2993,3078,3224,3311,3388,3442,3497,3563,3636,3712,3783,3861,3934,4010,4085,4155,4264,4352,4427,4519,4611,4685,4759,4851,4904,4986,5053,5136,5223,5285,5349,5412,5482,5596,5711,5813,5925,5983,6042,6127,6216,6300,6379"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,149,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3036,3117,3197,3279,3376,4184,4280,4404,6847,6910,6976,7388,7458,7522,7625,7688,7753,7813,7881,7944,7999,8127,8184,8246,8301,8376,8516,8603,8682,8775,8861,8944,9077,9159,9244,9390,9477,9554,9608,9663,9729,9802,9878,9949,10027,10100,10176,10251,10321,10430,10518,10593,10685,10777,10851,10925,11017,11070,11152,11219,11302,11389,11451,11515,11578,11648,11762,11877,11979,12091,12149,13634,14241,14330,14414", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,149,158,159,160", "endColumns": "12,80,79,81,96,88,95,123,86,62,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,78,92,85,82,132,81,84,145,86,76,53,54,65,72,75,70,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84,88,83,78", "endOffsets": "316,3112,3192,3274,3371,3460,4275,4399,4486,6905,6971,7062,7453,7517,7620,7683,7748,7808,7876,7939,7994,8122,8179,8241,8296,8371,8511,8598,8677,8770,8856,8939,9072,9154,9239,9385,9472,9549,9603,9658,9724,9797,9873,9944,10022,10095,10171,10246,10316,10425,10513,10588,10680,10772,10846,10920,11012,11065,11147,11214,11297,11384,11446,11510,11573,11643,11757,11872,11974,12086,12144,12203,13714,14325,14409,14488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\abafdc52b4a83dcb3e4911636b323609\\transformed\\play-services-base-18.5.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,688,829,958,1074,1180,1333,1436,1598,1727,1876,2031,2096,2156", "endColumns": "102,156,128,105,140,128,115,105,152,102,161,128,148,154,64,59,74", "endOffsets": "295,452,581,687,828,957,1073,1179,1332,1435,1597,1726,1875,2030,2095,2155,2230"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4491,4598,4759,4892,5002,5147,5280,5400,5647,5804,5911,6077,6210,6363,6522,6591,6655", "endColumns": "106,160,132,109,144,132,119,109,156,106,165,132,152,158,68,63,78", "endOffsets": "4593,4754,4887,4997,5142,5275,5395,5505,5799,5906,6072,6205,6358,6517,6586,6650,6729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5335a6eacc5065e32155bfa643f759a\\transformed\\core-1.16.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3465,3563,3665,3762,3860,3965,4068,14493", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "3558,3660,3757,3855,3960,4063,4179,14589"}}]}]}