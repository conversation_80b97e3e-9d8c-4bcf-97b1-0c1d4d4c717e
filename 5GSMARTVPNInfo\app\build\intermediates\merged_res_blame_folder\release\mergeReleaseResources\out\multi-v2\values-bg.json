{"logs": [{"outputFile": "com.official.fivegfastvpn.app-mergeReleaseResources-76:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1aae7574d5734885cc339c3642ba94ef\\transformed\\play-services-basement-18.4.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5645", "endColumns": "137", "endOffsets": "5778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a056806aa4aaa5631c39921f8bed3647\\transformed\\navigation-ui-2.9.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,123", "endOffsets": "163,287"}, "to": {"startLines": "136,137", "startColumns": "4,4", "startOffsets": "12593,12706", "endColumns": "112,123", "endOffsets": "12701,12825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e1c57ccd2dc8ee4086206221e4fbff22\\transformed\\material-1.12.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1118,1184,1281,1361,1423,1512,1575,1640,1699,1772,1835,1889,2017,2074,2136,2190,2263,2406,2490,2568,2661,2743,2831,2967,3055,3143,3279,3364,3441,3494,3545,3611,3686,3762,3833,3912,3989,4065,4142,4216,4328,4419,4494,4585,4677,4751,4838,4929,4984,5066,5132,5215,5301,5363,5427,5490,5560,5677,5789,5900,6010,6067,6122,6208,6299,6375", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,78,82,121,109,94,132,88,62,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85,90,75,78", "endOffsets": "260,339,418,501,623,733,828,961,1050,1113,1179,1276,1356,1418,1507,1570,1635,1694,1767,1830,1884,2012,2069,2131,2185,2258,2401,2485,2563,2656,2738,2826,2962,3050,3138,3274,3359,3436,3489,3540,3606,3681,3757,3828,3907,3984,4060,4137,4211,4323,4414,4489,4580,4672,4746,4833,4924,4979,5061,5127,5210,5296,5358,5422,5485,5555,5672,5784,5895,6005,6062,6117,6203,6294,6370,6449"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,149,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3064,3143,3222,3305,3427,4278,4373,4506,7013,7076,7142,7569,7649,7711,7800,7863,7928,7987,8060,8123,8177,8305,8362,8424,8478,8551,8694,8778,8856,8949,9031,9119,9255,9343,9431,9567,9652,9729,9782,9833,9899,9974,10050,10121,10200,10277,10353,10430,10504,10616,10707,10782,10873,10965,11039,11126,11217,11272,11354,11420,11503,11589,11651,11715,11778,11848,11965,12077,12188,12298,12355,13841,14461,14552,14628", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,149,158,159,160", "endColumns": "12,78,78,82,121,109,94,132,88,62,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85,90,75,78", "endOffsets": "310,3138,3217,3300,3422,3532,4368,4501,4590,7071,7137,7234,7644,7706,7795,7858,7923,7982,8055,8118,8172,8300,8357,8419,8473,8546,8689,8773,8851,8944,9026,9114,9250,9338,9426,9562,9647,9724,9777,9828,9894,9969,10045,10116,10195,10272,10348,10425,10499,10611,10702,10777,10868,10960,11034,11121,11212,11267,11349,11415,11498,11584,11646,11710,11773,11843,11960,12072,12183,12293,12350,12405,13922,14547,14623,14702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4174daf14328b785510b24290d95f19a\\transformed\\play-services-ads-24.3.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,249,298,370,439,508,618,681,813,924,1038,1088,1138,1246,1337,1391,1495,1530,1567,1627,1716,1759", "endColumns": "49,48,71,68,68,109,62,131,110,113,49,49,107,90,53,103,34,36,59,88,42,55", "endOffsets": "248,297,369,438,507,617,680,812,923,1037,1087,1137,1245,1336,1390,1494,1529,1566,1626,1715,1758,1814"}, "to": {"startLines": "133,134,135,138,139,140,141,142,143,144,145,146,147,148,150,151,152,153,154,155,156,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12410,12464,12517,12830,12903,12976,13090,13157,13293,13408,13526,13580,13634,13746,13927,13985,14093,14132,14173,14237,14330,14808", "endColumns": "53,52,75,72,72,113,66,135,114,117,53,53,111,94,57,107,38,40,63,92,46,59", "endOffsets": "12459,12512,12588,12898,12971,13085,13152,13288,13403,13521,13575,13629,13741,13836,13980,14088,14127,14168,14232,14325,14372,14863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5335a6eacc5065e32155bfa643f759a\\transformed\\core-1.16.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "38,39,40,41,42,43,44,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3537,3634,3744,3846,3947,4054,4159,14707", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3629,3739,3841,3942,4049,4154,4273,14803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b1798530a3a51669983781b25eadf6b2\\transformed\\browser-1.8.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,386", "endColumns": "110,107,111,109", "endOffsets": "161,269,381,491"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6902,7239,7347,7459", "endColumns": "110,107,111,109", "endOffsets": "7008,7342,7454,7564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\abafdc52b4a83dcb3e4911636b323609\\transformed\\play-services-base-18.5.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,459,590,697,860,991,1106,1211,1376,1484,1655,1789,1942,2104,2170,2225", "endColumns": "104,160,130,106,162,130,114,104,164,107,170,133,152,161,65,54,68", "endOffsets": "297,458,589,696,859,990,1105,1210,1375,1483,1654,1788,1941,2103,2169,2224,2293"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4595,4704,4869,5004,5115,5282,5417,5536,5783,5952,6064,6239,6377,6534,6700,6770,6829", "endColumns": "108,164,134,110,166,134,118,108,168,111,174,137,156,165,69,58,72", "endOffsets": "4699,4864,4999,5110,5277,5412,5531,5640,5947,6059,6234,6372,6529,6695,6765,6824,6897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\259d0aa544e023aed46b172a705803dc\\transformed\\appcompat-1.7.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,435,541,646,732,842,963,1043,1120,1211,1304,1399,1493,1593,1686,1781,1889,1980,2071,2154,2268,2376,2476,2590,2697,2805,2965,14377", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "430,536,641,727,837,958,1038,1115,1206,1299,1394,1488,1588,1681,1776,1884,1975,2066,2149,2263,2371,2471,2585,2692,2800,2960,3059,14456"}}]}]}