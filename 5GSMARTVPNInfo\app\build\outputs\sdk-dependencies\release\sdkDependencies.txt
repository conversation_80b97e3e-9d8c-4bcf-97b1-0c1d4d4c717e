# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.10.1"
  }
  digests {
    sha256: "\207R&m\314\267\322D\026\021<@\n\327\370\035\00253\376\330\353\200\017\2700\341\001b7D\342"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.1.10"
  }
  digests {
    sha256: "_*\301\312\215\310\263z?C\024\347\026\323ii\353\360\"zu\030\0352i\235\n\217d[\034!"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "2.1.0"
  }
  digests {
    sha256: "\375\352lB\003rOB\350\346K\357/\v\367\221)\314\321\337\036\337\034\317\375\302-\347\337I\214v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "2.1.0"
  }
  digests {
    sha256: "#\215<~I/\021\233P\332\034\"Tm\327bF.U\362$\ta\037^S\335wb\\\325D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.1.10"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-common"
    version: "8.10.1"
  }
  digests {
    sha256: "f\312\270&9\332\300\366\302C4d\300\223\260t\326\b\304\273\210~\303\212\233\213\304\254\230\022g2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-runtime"
    version: "8.10.1"
  }
  digests {
    sha256: "\303||J\263T3:\022\230\271+\3231\205+\230s{\347\004\372ey\021\314)\213u\264\261_"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\324\":\346\250Dvw&}\377\"\206;mi\210\312h\264\322|\246\327K\201\004k\004yBW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.9.0"
  }
  digests {
    sha256: "7\330\233!\001\360t\254l&\t\027\332\273\030V\ad^\342\000\2520\030\307\305\275\347\016\334\361\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-ktx"
    version: "1.2.0"
  }
  digests {
    sha256: "\303?\234\275\223\036a\220\3128\252\t\277\212z\212\0319\035K\017\267\247`ZkY\362\324%\200\321"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.2"
  }
  digests {
    sha256: "\337{\247?7}\275|\315(\261\022\257\320\215(\266\016\302\306\274\221\354e\206\t\310ED*\316\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.9.0"
  }
  digests {
    sha256: "\312\264\004\260]_\256\322\t\020J*\357\024\300\313\263\026\253\237\253\344\250\3607\260C\345\322\36445"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\372\235u\021\250\376\r^\334S\t\227\006\326O7b\035\317\353\237,Usi\355\203d_\310z#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.9.0"
  }
  digests {
    sha256: "I}\330M\351\362\375F2O%\371\243\257\003-:#\320\207\350z\365\025\304\261\2245\366\326\246Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.9.0"
  }
  digests {
    sha256: "F8-\337\2724\215\271xF\317\250\035g\327#\004\334\025k\271b$5\213B\237\347H\274j\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.9.0"
  }
  digests {
    sha256: "d\371kR\307\b\225\200\1770 \342\350\372\021\363\354-\251\301W\366*\256!\242\311\323\264\204\247G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\362e\246e\201\236Q2+>\352*o\362\253\220\030l\255\361xz\201\335\213\357\f\351<J`\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.9.0"
  }
  digests {
    sha256: "\301\\\351r\314(\223jYj\234\322V\263/\"v\212a\325\335\201\017\245k\251ER*\016\347\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.9.0"
  }
  digests {
    sha256: "3?\242\316m\267\n\303r\267\221\337\353\251\234\002\300\331{\032\213J\264\250MGbFm\022\262\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.9.0"
  }
  digests {
    sha256: "\307\205\200\3128\315\024\344t28a\353\350\"\320L\004#\034\267\345\233$\336\200\024\006\245\0217j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\257B\375\204\364\345+D\334\253\257\236\317\224\022\005S\222\233\301\240Y\276\262$\n\236\301\333\356\201O"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\3336x8qnU\206h#\306\336\355b\374\354#\374\344\207\277*\217\245e\243o\024\232\263\321\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-android"
    version: "1.3.0"
  }
  digests {
    sha256: "!\vi\t\273\325\025\333\364\025\220\\\273\"\017\312\223\250\177\245F\203\217u@w\353\310\340H\205\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\360\255\336E\206ADGS\205\317J\247\340\267\376\262\177a\374\371G&e\355\230\314\227\033\006\261\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.3.0"
  }
  digests {
    sha256: "E\305\366A(@\365\r\347\277}\233\034J\214\201\352#h2Hz\0218\3132\v\214\231t\026\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\363\226\365\215\275w,\006[\2076\226\267J=M\buT\263vb\200\346;I\262]\273S\253\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.sentry"
    artifactId: "sentry-android-fragment"
    version: "8.1.0"
  }
  digests {
    sha256: "~\215\335\322\372\306-\270\\$\305\237N\305\225X\361k\335\232}6\316\365\3421\004\344\'\323\253$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.sentry"
    artifactId: "sentry"
    version: "8.1.0"
  }
  digests {
    sha256: "\223#\034\204\334\354\243$G-\r\3701\224\031\343\265\355Cf\225\251\255ZK\374:D+q\315>"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.2"
  }
  digests {
    sha256: "F\f\2206\266M\247\316z\006j\f\261\204\340\024Q\317\304I)\252\033b\376\006\211Y\177#C\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.sentry"
    artifactId: "sentry-kotlin-extensions"
    version: "8.1.0"
  }
  digests {
    sha256: "H\277\352\226\335.\023\205pbF/\306\333\330\364\305\272\230\310*\376\376\246\266D\323Pl\235)B"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-adapters"
    version: "8.10.1"
  }
  digests {
    sha256: "\363\217*=L\372\327\220\325\360<g\271\'\315V\205\214\251\366\261\020^\263\256L/\305#\313C}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-ktx"
    version: "8.10.1"
  }
  digests {
    sha256: "\375t3\317l\336Pa\360\326\323\201=Z\353\277\334?\230\"\306\270S\025\270v\223\343X2\375%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads"
    version: "24.3.0"
  }
  digests {
    sha256: "\032s\370:i\324\264\247\360\325D\276!\235\\\3205\244X\333\240\315\270\364[gV\374\300\362\317\350"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\001\'W\214\334\355\255|\216\316\027H\024\306\364\343\002x\216\217{\321N\203=\221\234\033vY\331}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\227\352S+F\274\203\365\254\344\a\342B\247\254\315\330+\204\2407u\331n\257\\\341`\316\275\273\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.11.0-alpha02"
  }
  digests {
    sha256: ";\037^#n\221\231\2518\375\vP{\306\316q\034F\315\002\206\275@\246\330r\336@\232\267\331N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-api"
    version: "24.3.0"
  }
  digests {
    sha256: "\340\2463\363P\003\020;Y\'\375q\206\272\260H_W}>\342\210\343Q\vV\307\236V\276V\206"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.10.1"
  }
  digests {
    sha256: "\305\271xs\230~P\373\301\235\334\253\227\032j\017\020\200\207B:\214\353\321\\\'M\260\320~\"E"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "\031h\277R\003\2368cj\246\361\024\315\027\327%i\031\321\350\231t\027qo\357\235\035\241\362M\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.sentry"
    artifactId: "sentry-android-sqlite"
    version: "8.1.0"
  }
  digests {
    sha256: "\250k\306\231\236\335\2112\026\210\224\231R\022y\243]\207\327\275\360\325\213(\236\034\247\022\320H\310}"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "20.1.2"
  }
  digests {
    sha256: "\315\221\rE\276~\276;p\311\244\301l\233\356e\350t\201\025\355@\0226C\230O5\351\033_8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "20.1.2"
  }
  digests {
    sha256: "\356\224}z\017\342,Z2\357l\233oz\020z\352j\320pDu\207N\326is\bC\034{\276"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.ump"
    artifactId: "user-messaging-platform"
    version: "3.2.0"
  }
  digests {
    sha256: "\027\262\303\364Th\313\2057\246d\353x8\\\006\221\027\212\2534\020,\246\350U\220(\037!&&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-appset"
    version: "16.0.1"
  }
  digests {
    sha256: "\340N\315Ou\317E\312,7\273S\373 \224Z\234Z2,\025\341l$\031pmg/\260\020\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.2.1"
  }
  digests {
    sha256: "0\370\327\233x-(:\220\360\266\3676\221i\331\317V\000\221S\177\335`V\321\332\251\363\0037c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.1.1"
  }
  digests {
    sha256: "< T2\203(\203\036\266\346\233@\024\366\357\237\252\021\177\324\270\021\222\237:\221\323\3337_,\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.1.0"
  }
  digests {
    sha256: "\242+\224\247w\211\363\263K\354\322@\202#\023\000R@\021\314\310?\002aB\304\232\326\00117\233"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment"
    version: "2.9.0"
  }
  digests {
    sha256: "&fu\2619\b\304ZIt\003C\3766.+k\240\241\364[q\332\340\301G\213\365\344\207O\354"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-android"
    version: "2.9.0"
  }
  digests {
    sha256: "]\257U\342\a\005\240G\036v\332\022\210p\30315\034Z\022<\316\364\244\262G~\271-7\331{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\367o\004\270O\310\233t\000\023\272\201\313\f\035\351\024\005=\325\\\362\330\360\306i\304\326\362\270\353\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui"
    version: "2.9.0"
  }
  digests {
    sha256: "\304\017_X\346\204=\216\017\367`\210\260\b\\\005\310\357\237X-\317\334\327$\213^P\232\016q>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0"
  }
  digests {
    sha256: ",\347\220l\321\336\240Z\354\201\227]\262-T8#Y\300Z!\262Rz\330H\274`\366\262r\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "pl.droidsonroids.gif"
    artifactId: "android-gif-drawable"
    version: "1.2.28"
  }
  digests {
    sha256: "!\203\337\364\022h\021\347\322\003\227\321\230\233\177jW{\016\226\325\222\234\ntT|\301w\f\216\250"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.getkeepsafe.relinker"
    artifactId: "relinker"
    version: "1.4.5"
  }
  digests {
    sha256: "\260;M\021:\237\357y@\n\350T\301wW\302\221W%\346\377\321\326\021\026\352\332\353V\0237J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.16.0"
  }
  digests {
    sha256: "\211\201\034c\335&jHQ\375\033y\306\374\f\230)\226\354\3445\365\377H?\2700\312l\265<\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.16.0"
  }
  digests {
    sha256: "\225_\207*\364\322\243!\376\242\243F\374G\"%\242\271\225$\254\304g\201I\027\022\343\233j!O"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.16.0"
  }
  digests {
    sha256: "\242\'\366U\234\020J\245\245\310\213\np\351\307\343\333hY\343\vGt\363\202\221!\337\236bst"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.16.0"
  }
  digests {
    sha256: "\305\2369\261\263\033MU\222\'|\331\000\022\024\235t\vV\235C8\365dC\312\310,?\275\243\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.android.billingclient"
    artifactId: "billing"
    version: "7.1.1"
  }
  digests {
    sha256: "\254\0352\372\000w\301\032\322b\335\004\215\336C&\037\303\005\220@\361R\005\215!\311F\003\271e\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.9"
  }
  digests {
    sha256: "\a\243 %\246[\b\356~\021\321M\3059\247X\266g\023\360\301\3219\000\250\025\231\316\255\272\364M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.9"
  }
  digests {
    sha256: "At\\[\217B}$C\220\025\270Oe\032\324 q\211\221\206}*\374&,&@\t\270\002\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-location"
    version: "19.0.0"
  }
  digests {
    sha256: "k \\C\272]\367Q\354\250\316\235\256zX\357\372\372\307\3267\373O\307\b\247R-\033\231\317\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-places-placereport"
    version: "17.0.0"
  }
  digests {
    sha256: ",\177\326:\320/(\025\n\344\377\344a]\254}iMy\016,Fg\367w\256\334\216\340T\351)"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "app-update"
    version: "2.1.0"
  }
  digests {
    sha256: "\025e\351\357\001O|\267\031?\004b\344\330\027H\n\377K3?\307\367\340\005+\2712\314(\202\r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "core-common"
    version: "2.0.3"
  }
  digests {
    sha256: "C\003%_\025,Y\271T\221\233\264q\321N\345\376\322\337x5e\336\250R\213\b\b;\2474+"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.android.volley"
    artifactId: "volley"
    version: "1.2.1"
  }
  digests {
    sha256: "\333\360J#\377\221\266quq\257\347\"\376K\325wbW\365\221\021\327\302\315\373:\235_\210\210\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.ads.mediation"
    artifactId: "facebook"
    version: "6.20.0.0"
  }
  digests {
    sha256: "\244\001\210i!\021uu\341q\347\257\207\265@\301\213\260z\312\250\232L\315h\377\261\006\225\316\2320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.facebook.android"
    artifactId: "audience-network-sdk"
    version: "6.20.0"
  }
  digests {
    sha256: "\337\005\344Ys\256\203\210\344\021L%+vsK\323q\214N\323\001~\033y^\352\033\301&*\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jacoco"
    artifactId: "org.jacoco.core"
    version: "0.8.8"
  }
  digests {
    sha256: "GLx/\200\235\210\222G\023\337\333\360\254\267\2353\017\220K\345vHH\003F=\004ea\026C"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.ow2.asm"
    artifactId: "asm"
    version: "9.2"
  }
  digests {
    sha256: "\271\324\376Mq\223\215\363\2109\360\354\244*\252\246L\370\263\023\326x\332\003o\f\263\312\031\233G\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.ow2.asm"
    artifactId: "asm-commons"
    version: "9.2"
  }
  digests {
    sha256: "\276L\34518\2428\273R,\327\201\317\221\363\272\\\342\366\312\223\354b\324j\026*\022r%\340\246"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.ow2.asm"
    artifactId: "asm-tree"
    version: "9.2"
  }
  digests {
    sha256: "\252\277\233\3220\221\244\353\374\020\234\037>\347\317>K\211\366\272-?Q\305$?\026\263\317\372\340\021"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.ow2.asm"
    artifactId: "asm-analysis"
    version: "9.2"
  }
  digests {
    sha256: "\207\217\276R\0271\300r\321M-e\271\203\261\276\256j\320o\332\000\a\266\250\272\350\037s\3643\304"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.infer.annotation"
    artifactId: "infer-annotation"
    version: "0.18.0"
  }
  digests {
    sha256: "\312\357\272\223VC\334\226\'X\247`\361bz@\"\025\257\003M\226\300\364\204\326;\177\335\312\231/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-annotations-jvm"
    version: "1.3.72"
  }
  digests {
    sha256: ">\343\245m\324Q\343?\203R\340\322\200:U\312\033\221V\327\370\250\340u\233\370\341\207\377\215\262J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.1.1"
  }
  digests {
    sha256: "K\301\327\263\205\307\r\374\006\330c\302\321\020|<\206cS\247\030\020\364\036D\227\250\355\335\231\027\216"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.2.0"
  }
  digests {
    sha256: "\262\262\217k\241s\365\340\304\376=6\242\327\356R9\307\254\277AC\265I\354\3601\243H[5\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.2.0"
  }
  digests {
    sha256: "\336\001\036j;ya\336c\217\027/,\266k\243\004\352\251\211f*\236+\017H\337\367t\314\301f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie"
    version: "6.6.1"
  }
  digests {
    sha256: "l\220\342\367S\350Z\217\177\035V\253\301\343\vf\204\302\264\245\n\272cB\365c\027\017\273BY\303"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "1.17.6"
  }
  digests {
    sha256: "\216\210\260UR<\310\006\023\337\270D\325\230t\016\020\255\246/K\331\203\004\315\321@\305\205L\303\266"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.sentry"
    artifactId: "sentry-android"
    version: "8.1.0"
  }
  digests {
    sha256: "th\241\206j\306\227\214\237\277\220\261\264W\262\ta\254\311\257\271W\224\003\334Y\234\033\322\\%\271"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.sentry"
    artifactId: "sentry-android-core"
    version: "8.1.0"
  }
  digests {
    sha256: "\244WsK<\275\236\356\2111\351\262\244\"\347j\306a\255\205\355\230\r\023\362\360\256\321\340nQ5"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.sentry"
    artifactId: "sentry-android-ndk"
    version: "8.1.0"
  }
  digests {
    sha256: "\206\236\254\351\307j\316\017@\342`\260\016X\312,\310\3765\f\215\221L\211`0\247B\226\256\364m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.sentry"
    artifactId: "sentry-native-ndk"
    version: "0.7.19"
  }
  digests {
    sha256: "\360\316\352\253\322`\200%\b\273\276\356\264m\226\346MZ\3454\362k\246\272\030\364E\372?\242\210\273"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.sentry"
    artifactId: "sentry-android-replay"
    version: "8.1.0"
  }
  digests {
    sha256: "\370?\377dt?*\211\367\312\307\006\360\270\373\250sA\233\006\355|\213?\263\332\367O\357\346\352\003"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
  library_dep_index: 5
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 13
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 14
}
library_dependencies {
  library_index: 14
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 59
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
}
library_dependencies {
  library_index: 16
  library_dep_index: 1
  library_dep_index: 15
}
library_dependencies {
  library_index: 17
  library_dep_index: 3
  library_dep_index: 3
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 19
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 35
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
  library_dep_index: 67
}
library_dependencies {
  library_index: 21
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 7
  library_dep_index: 6
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 20
  library_dep_index: 24
}
library_dependencies {
  library_index: 23
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 6
}
library_dependencies {
  library_index: 24
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 25
  library_dep_index: 6
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 10
  library_dep_index: 27
  library_dep_index: 37
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
  library_dep_index: 28
  library_dep_index: 10
  library_dep_index: 29
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 3
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 3
}
library_dependencies {
  library_index: 28
  library_dep_index: 3
}
library_dependencies {
  library_index: 29
  library_dep_index: 1
  library_dep_index: 30
}
library_dependencies {
  library_index: 31
  library_dep_index: 1
}
library_dependencies {
  library_index: 32
  library_dep_index: 1
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 32
  library_dep_index: 3
  library_dep_index: 32
}
library_dependencies {
  library_index: 34
  library_dep_index: 1
  library_dep_index: 10
}
library_dependencies {
  library_index: 36
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 3
  library_dep_index: 27
  library_dep_index: 3
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
  library_dep_index: 1
  library_dep_index: 28
  library_dep_index: 10
  library_dep_index: 36
  library_dep_index: 43
  library_dep_index: 13
  library_dep_index: 39
  library_dep_index: 51
  library_dep_index: 61
  library_dep_index: 59
  library_dep_index: 53
  library_dep_index: 62
  library_dep_index: 3
  library_dep_index: 64
  library_dep_index: 66
}
library_dependencies {
  library_index: 38
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 13
  library_dep_index: 39
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 53
  library_dep_index: 32
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 3
  library_dep_index: 60
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
  library_dep_index: 17
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 20
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 41
  library_dep_index: 1
  library_dep_index: 18
  library_dep_index: 18
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 51
}
library_dependencies {
  library_index: 42
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 43
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 3
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 44
  library_dep_index: 43
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 45
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 46
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
  library_dep_index: 32
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 48
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 13
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 49
  library_dep_index: 13
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 50
  library_dep_index: 39
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 51
  library_dep_index: 52
}
library_dependencies {
  library_index: 52
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 43
  library_dep_index: 39
  library_dep_index: 53
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 20
  library_dep_index: 55
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 3
}
library_dependencies {
  library_index: 53
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 1
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 55
  library_dep_index: 58
  library_dep_index: 3
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
  library_dep_index: 3
}
library_dependencies {
  library_index: 57
  library_dep_index: 56
  library_dep_index: 55
}
library_dependencies {
  library_index: 58
  library_dep_index: 53
  library_dep_index: 3
  library_dep_index: 53
  library_dep_index: 3
}
library_dependencies {
  library_index: 59
  library_dep_index: 1
  library_dep_index: 29
  library_dep_index: 46
  library_dep_index: 30
}
library_dependencies {
  library_index: 60
  library_dep_index: 38
  library_dep_index: 47
  library_dep_index: 50
  library_dep_index: 58
  library_dep_index: 38
}
library_dependencies {
  library_index: 61
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 42
  library_dep_index: 39
}
library_dependencies {
  library_index: 62
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 63
}
library_dependencies {
  library_index: 63
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 10
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 66
  library_dep_index: 60
  library_dep_index: 12
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 44
  library_dep_index: 50
  library_dep_index: 58
  library_dep_index: 3
  library_dep_index: 37
}
library_dependencies {
  library_index: 67
  library_dep_index: 65
}
library_dependencies {
  library_index: 68
  library_dep_index: 9
  library_dep_index: 8
}
library_dependencies {
  library_index: 69
  library_dep_index: 9
  library_dep_index: 6
  library_dep_index: 23
  library_dep_index: 47
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 49
  library_dep_index: 39
}
library_dependencies {
  library_index: 70
  library_dep_index: 38
  library_dep_index: 1
  library_dep_index: 71
  library_dep_index: 10
  library_dep_index: 27
  library_dep_index: 36
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 37
  library_dep_index: 13
  library_dep_index: 39
  library_dep_index: 59
  library_dep_index: 78
  library_dep_index: 53
  library_dep_index: 3
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 27
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 70
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 10
}
library_dependencies {
  library_index: 73
  library_dep_index: 72
  library_dep_index: 31
  library_dep_index: 10
}
library_dependencies {
  library_index: 74
  library_dep_index: 1
}
library_dependencies {
  library_index: 75
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 63
}
library_dependencies {
  library_index: 76
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 27
  library_dep_index: 45
  library_dep_index: 46
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 10
  library_dep_index: 27
  library_dep_index: 76
  library_dep_index: 76
}
library_dependencies {
  library_index: 78
  library_dep_index: 1
}
library_dependencies {
  library_index: 79
  library_dep_index: 80
  library_dep_index: 10
  library_dep_index: 27
  library_dep_index: 81
  library_dep_index: 82
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 26
  library_dep_index: 25
  library_dep_index: 30
}
library_dependencies {
  library_index: 80
  library_dep_index: 1
  library_dep_index: 28
  library_dep_index: 10
  library_dep_index: 29
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 30
}
library_dependencies {
  library_index: 81
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 82
}
library_dependencies {
  library_index: 82
  library_dep_index: 1
  library_dep_index: 29
  library_dep_index: 36
  library_dep_index: 81
  library_dep_index: 83
  library_dep_index: 30
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 81
}
library_dependencies {
  library_index: 83
  library_dep_index: 84
  library_dep_index: 30
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 87
  library_dep_index: 88
}
library_dependencies {
  library_index: 89
  library_dep_index: 1
  library_dep_index: 27
}
library_dependencies {
  library_index: 90
  library_dep_index: 80
  library_dep_index: 91
  library_dep_index: 26
  library_dep_index: 99
  library_dep_index: 101
}
library_dependencies {
  library_index: 91
  library_dep_index: 28
  library_dep_index: 92
  library_dep_index: 27
  library_dep_index: 42
  library_dep_index: 49
  library_dep_index: 93
  library_dep_index: 46
  library_dep_index: 33
  library_dep_index: 30
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 3
}
library_dependencies {
  library_index: 92
  library_dep_index: 29
  library_dep_index: 3
  library_dep_index: 20
}
library_dependencies {
  library_index: 93
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 94
  library_dep_index: 95
}
library_dependencies {
  library_index: 94
  library_dep_index: 1
  library_dep_index: 6
  library_dep_index: 93
  library_dep_index: 95
}
library_dependencies {
  library_index: 95
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 94
  library_dep_index: 96
  library_dep_index: 98
  library_dep_index: 94
  library_dep_index: 93
}
library_dependencies {
  library_index: 96
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 97
  library_dep_index: 98
}
library_dependencies {
  library_index: 97
  library_dep_index: 6
  library_dep_index: 65
}
library_dependencies {
  library_index: 98
  library_dep_index: 1
  library_dep_index: 96
  library_dep_index: 3
  library_dep_index: 96
}
library_dependencies {
  library_index: 99
  library_dep_index: 26
  library_dep_index: 100
}
library_dependencies {
  library_index: 100
  library_dep_index: 26
}
library_dependencies {
  library_index: 101
  library_dep_index: 1
  library_dep_index: 26
}
library_dependencies {
  library_index: 102
  library_dep_index: 26
}
library_dependencies {
  library_index: 103
  library_dep_index: 104
  library_dep_index: 26
  library_dep_index: 25
}
library_dependencies {
  library_index: 104
  library_dep_index: 10
  library_dep_index: 27
  library_dep_index: 37
  library_dep_index: 26
  library_dep_index: 25
}
library_dependencies {
  library_index: 105
  library_dep_index: 70
  library_dep_index: 106
  library_dep_index: 27
  library_dep_index: 59
}
library_dependencies {
  library_index: 106
  library_dep_index: 1
}
library_dependencies {
  library_index: 107
  library_dep_index: 1
}
library_dependencies {
  library_index: 108
  library_dep_index: 109
  library_dep_index: 87
  library_dep_index: 38
  library_dep_index: 1
  library_dep_index: 70
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 105
  library_dep_index: 27
  library_dep_index: 75
  library_dep_index: 112
  library_dep_index: 28
  library_dep_index: 37
  library_dep_index: 13
  library_dep_index: 116
  library_dep_index: 78
  library_dep_index: 117
  library_dep_index: 72
  library_dep_index: 118
}
library_dependencies {
  library_index: 109
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 110
  library_dep_index: 1
}
library_dependencies {
  library_index: 111
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 63
  library_dep_index: 10
}
library_dependencies {
  library_index: 112
  library_dep_index: 27
  library_dep_index: 10
  library_dep_index: 113
}
library_dependencies {
  library_index: 113
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 114
  library_dep_index: 61
  library_dep_index: 107
  library_dep_index: 115
}
library_dependencies {
  library_index: 114
  library_dep_index: 1
}
library_dependencies {
  library_index: 115
  library_dep_index: 1
}
library_dependencies {
  library_index: 116
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 63
  library_dep_index: 10
}
library_dependencies {
  library_index: 117
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 27
  library_dep_index: 112
}
library_dependencies {
  library_index: 118
  library_dep_index: 1
  library_dep_index: 37
  library_dep_index: 116
  library_dep_index: 27
  library_dep_index: 10
}
library_dependencies {
  library_index: 119
  library_dep_index: 38
  library_dep_index: 36
  library_dep_index: 66
  library_dep_index: 18
  library_dep_index: 43
  library_dep_index: 39
  library_dep_index: 120
  library_dep_index: 122
  library_dep_index: 53
  library_dep_index: 125
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 55
  library_dep_index: 120
  library_dep_index: 122
  library_dep_index: 124
  library_dep_index: 3
}
library_dependencies {
  library_index: 120
  library_dep_index: 121
}
library_dependencies {
  library_index: 121
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 36
  library_dep_index: 18
  library_dep_index: 13
  library_dep_index: 39
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 53
  library_dep_index: 58
  library_dep_index: 3
  library_dep_index: 55
  library_dep_index: 119
  library_dep_index: 122
  library_dep_index: 124
  library_dep_index: 3
}
library_dependencies {
  library_index: 122
  library_dep_index: 123
}
library_dependencies {
  library_index: 123
  library_dep_index: 60
  library_dep_index: 28
  library_dep_index: 10
  library_dep_index: 36
  library_dep_index: 18
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 120
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 55
  library_dep_index: 120
  library_dep_index: 119
  library_dep_index: 124
  library_dep_index: 3
}
library_dependencies {
  library_index: 124
  library_dep_index: 70
  library_dep_index: 111
  library_dep_index: 36
  library_dep_index: 63
  library_dep_index: 75
  library_dep_index: 120
  library_dep_index: 122
  library_dep_index: 117
  library_dep_index: 108
  library_dep_index: 120
  library_dep_index: 119
  library_dep_index: 122
}
library_dependencies {
  library_index: 125
  library_dep_index: 1
  library_dep_index: 63
  library_dep_index: 27
  library_dep_index: 126
  library_dep_index: 117
}
library_dependencies {
  library_index: 126
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 27
}
library_dependencies {
  library_index: 127
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 31
}
library_dependencies {
  library_index: 128
  library_dep_index: 129
}
library_dependencies {
  library_index: 130
  library_dep_index: 131
  library_dep_index: 132
  library_dep_index: 133
  library_dep_index: 37
  library_dep_index: 73
  library_dep_index: 134
  library_dep_index: 32
}
library_dependencies {
  library_index: 131
  library_dep_index: 1
}
library_dependencies {
  library_index: 134
  library_dep_index: 1
}
library_dependencies {
  library_index: 135
  library_dep_index: 38
  library_dep_index: 136
  library_dep_index: 137
  library_dep_index: 138
  library_dep_index: 104
  library_dep_index: 26
  library_dep_index: 143
  library_dep_index: 25
}
library_dependencies {
  library_index: 136
  library_dep_index: 1
}
library_dependencies {
  library_index: 137
  library_dep_index: 136
  library_dep_index: 138
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 1
}
library_dependencies {
  library_index: 138
  library_dep_index: 136
  library_dep_index: 1
  library_dep_index: 139
  library_dep_index: 140
  library_dep_index: 141
}
library_dependencies {
  library_index: 140
  library_dep_index: 1
}
library_dependencies {
  library_index: 141
  library_dep_index: 1
  library_dep_index: 140
}
library_dependencies {
  library_index: 142
  library_dep_index: 1
  library_dep_index: 140
}
library_dependencies {
  library_index: 143
  library_dep_index: 104
  library_dep_index: 26
  library_dep_index: 144
  library_dep_index: 25
}
library_dependencies {
  library_index: 144
  library_dep_index: 26
}
library_dependencies {
  library_index: 145
  library_dep_index: 26
  library_dep_index: 25
  library_dep_index: 146
}
library_dependencies {
  library_index: 148
  library_dep_index: 149
  library_dep_index: 1
  library_dep_index: 79
  library_dep_index: 150
  library_dep_index: 3
}
library_dependencies {
  library_index: 149
  library_dep_index: 26
}
library_dependencies {
  library_index: 150
  library_dep_index: 151
  library_dep_index: 152
  library_dep_index: 153
}
library_dependencies {
  library_index: 152
  library_dep_index: 151
  library_dep_index: 153
  library_dep_index: 154
}
library_dependencies {
  library_index: 153
  library_dep_index: 151
}
library_dependencies {
  library_index: 154
  library_dep_index: 153
}
library_dependencies {
  library_index: 155
  library_dep_index: 85
  library_dep_index: 156
}
library_dependencies {
  library_index: 157
  library_dep_index: 158
  library_dep_index: 161
  library_dep_index: 159
  library_dep_index: 162
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 141
  library_dep_index: 163
  library_dep_index: 164
  library_dep_index: 165
  library_dep_index: 166
  library_dep_index: 1
  library_dep_index: 136
  library_dep_index: 137
  library_dep_index: 138
  library_dep_index: 104
  library_dep_index: 26
  library_dep_index: 25
  library_dep_index: 167
  library_dep_index: 168
  library_dep_index: 87
  library_dep_index: 3
}
library_dependencies {
  library_index: 158
  library_dep_index: 24
  library_dep_index: 159
  library_dep_index: 160
  library_dep_index: 1
  library_dep_index: 29
  library_dep_index: 3
  library_dep_index: 26
  library_dep_index: 25
}
library_dependencies {
  library_index: 159
  library_dep_index: 160
  library_dep_index: 1
  library_dep_index: 87
}
library_dependencies {
  library_index: 160
  library_dep_index: 139
}
library_dependencies {
  library_index: 161
  library_dep_index: 158
  library_dep_index: 6
  library_dep_index: 159
  library_dep_index: 160
}
library_dependencies {
  library_index: 162
  library_dep_index: 136
  library_dep_index: 138
  library_dep_index: 137
  library_dep_index: 1
}
library_dependencies {
  library_index: 163
  library_dep_index: 26
  library_dep_index: 25
}
library_dependencies {
  library_index: 164
  library_dep_index: 165
  library_dep_index: 3
  library_dep_index: 25
  library_dep_index: 160
  library_dep_index: 158
  library_dep_index: 161
  library_dep_index: 159
}
library_dependencies {
  library_index: 165
  library_dep_index: 25
  library_dep_index: 160
}
library_dependencies {
  library_index: 166
  library_dep_index: 26
  library_dep_index: 160
}
library_dependencies {
  library_index: 167
  library_dep_index: 26
  library_dep_index: 25
}
library_dependencies {
  library_index: 168
  library_dep_index: 113
  library_dep_index: 26
}
library_dependencies {
  library_index: 169
  library_dep_index: 170
  library_dep_index: 70
}
library_dependencies {
  library_index: 171
  library_dep_index: 172
  library_dep_index: 173
  library_dep_index: 175
}
library_dependencies {
  library_index: 172
  library_dep_index: 45
  library_dep_index: 41
  library_dep_index: 27
  library_dep_index: 65
}
library_dependencies {
  library_index: 173
  library_dep_index: 174
  library_dep_index: 65
  library_dep_index: 172
}
library_dependencies {
  library_index: 175
  library_dep_index: 6
  library_dep_index: 65
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 8
  dependency_index: 9
  dependency_index: 68
  dependency_index: 69
  dependency_index: 3
  dependency_index: 36
  dependency_index: 70
  dependency_index: 108
  dependency_index: 38
  dependency_index: 105
  dependency_index: 119
  dependency_index: 124
  dependency_index: 127
  dependency_index: 128
  dependency_index: 130
  dependency_index: 135
  dependency_index: 145
  dependency_index: 147
  dependency_index: 107
  dependency_index: 91
  dependency_index: 79
  dependency_index: 148
  dependency_index: 149
  dependency_index: 155
  dependency_index: 101
  dependency_index: 157
  dependency_index: 169
  dependency_index: 171
  dependency_index: 87
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
