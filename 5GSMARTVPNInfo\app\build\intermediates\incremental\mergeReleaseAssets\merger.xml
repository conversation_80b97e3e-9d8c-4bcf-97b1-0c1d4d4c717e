<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.facebook.android:audience-network-sdk:6.20.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\assets"><file name="audience_network.dex" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\assets\audience_network.dex"/></source></dataSet><dataSet config=":vpnLib" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\assets\release\mergeReleaseAssets"><file name="nopie_openvpn.arm64-v8a" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\assets\release\mergeReleaseAssets\nopie_openvpn.arm64-v8a"/><file name="nopie_openvpn.armeabi-v7a" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\assets\release\mergeReleaseAssets\nopie_openvpn.armeabi-v7a"/><file name="nopie_openvpn.x86" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\assets\release\mergeReleaseAssets\nopie_openvpn.x86"/><file name="nopie_openvpn.x86_64" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\assets\release\mergeReleaseAssets\nopie_openvpn.x86_64"/><file name="pie_openvpn.arm64-v8a" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\assets\release\mergeReleaseAssets\pie_openvpn.arm64-v8a"/><file name="pie_openvpn.armeabi-v7a" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\assets\release\mergeReleaseAssets\pie_openvpn.armeabi-v7a"/><file name="pie_openvpn.x86" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\assets\release\mergeReleaseAssets\pie_openvpn.x86"/><file name="pie_openvpn.x86_64" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\assets\release\mergeReleaseAssets\pie_openvpn.x86_64"/></source></dataSet><dataSet config=":nativetemplates" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\nativetemplates\build\intermediates\assets\release\mergeReleaseAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\release\assets"/></dataSet><dataSet config="assets-collectExternalReleaseDependenciesForSentry" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\build\generated\assets\collectExternalReleaseDependenciesForSentry"><file name="sentry-external-modules.txt" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\build\generated\assets\collectExternalReleaseDependenciesForSentry\sentry-external-modules.txt"/></source></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\build\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>