# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /Users/<USER>/Library/Android/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}


-keep class com.github.mikephil.charting.** { *; }
-dontwarn io.realm.**

# Keep all OpenVPN classes
-keep class de.blinkt.openvpn.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep VPN Service
-keep class * extends android.net.VpnService { *; }

# Keep AIDL interfaces
-keep interface de.blinkt.openvpn.api.** { *; }

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable { *; }

# Keep broadcast receivers
-keep class * extends android.content.BroadcastReceiver { *; }

# Keep service classes
-keep class * extends android.app.Service { *; }

# Keep state listeners and callbacks
-keep interface de.blinkt.openvpn.core.VpnStatus$StateListener { *; }
-keep interface de.blinkt.openvpn.core.VpnStatus$LogListener { *; }
-keep interface de.blinkt.openvpn.core.VpnStatus$ByteCountListener { *; }

# Keep OpenVPN management interface
-keep interface de.blinkt.openvpn.core.OpenVPNManagement { *; }

# Preserve line numbers for debugging
-keepattributes SourceFile,LineNumberTable