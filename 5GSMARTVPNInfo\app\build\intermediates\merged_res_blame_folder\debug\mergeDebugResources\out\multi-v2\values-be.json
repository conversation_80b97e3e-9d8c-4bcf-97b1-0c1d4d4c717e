{"logs": [{"outputFile": "com.official.fivegfastvpn.app-mergeDebugResources-76:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\259d0aa544e023aed46b172a705803dc\\transformed\\appcompat-1.7.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,14380", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,14457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a056806aa4aaa5631c39921f8bed3647\\transformed\\navigation-ui-2.9.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,125", "endOffsets": "159,285"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "12574,12683", "endColumns": "108,125", "endOffsets": "12678,12804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1aae7574d5734885cc339c3642ba94ef\\transformed\\play-services-basement-18.4.0\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5634", "endColumns": "145", "endOffsets": "5775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\abafdc52b4a83dcb3e4911636b323609\\transformed\\play-services-base-18.5.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4630,4737,4899,5024,5134,5289,5415,5530,5780,5942,6049,6212,6340,6493,6652,6721,6783", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "4732,4894,5019,5129,5284,5410,5525,5629,5937,6044,6207,6335,6488,6647,6716,6778,6857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5335a6eacc5065e32155bfa643f759a\\transformed\\core-1.16.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3587,3685,3787,3887,3988,4094,4197,14702", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3680,3782,3882,3983,4089,4192,4313,14798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e1c57ccd2dc8ee4086206221e4fbff22\\transformed\\material-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1197,1264,1357,1434,1497,1613,1676,1745,1804,1875,1934,1988,2109,2170,2233,2287,2360,2482,2570,2646,2737,2818,2901,3053,3139,3226,3359,3450,3533,3590,3641,3707,3779,3856,3927,4010,4085,4162,4244,4320,4428,4517,4599,4690,4786,4860,4941,5036,5090,5172,5238,5325,5411,5473,5537,5600,5669,5779,5892,5995,6102,6163,6218,6298,6383,6459", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,76,76,81,96,91,96,131,82,77,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,132,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79,84,75,78", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1192,1259,1352,1429,1492,1608,1671,1740,1799,1870,1929,1983,2104,2165,2228,2282,2355,2477,2565,2641,2732,2813,2896,3048,3134,3221,3354,3445,3528,3585,3636,3702,3774,3851,3922,4005,4080,4157,4239,4315,4423,4512,4594,4685,4781,4855,4936,5031,5085,5167,5233,5320,5406,5468,5532,5595,5664,5774,5887,5990,6097,6158,6213,6293,6378,6454,6533"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,151,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3162,3239,3316,3398,3495,4318,4415,4547,6971,7049,7116,7536,7613,7676,7792,7855,7924,7983,8054,8113,8167,8288,8349,8412,8466,8539,8661,8749,8825,8916,8997,9080,9232,9318,9405,9538,9629,9712,9769,9820,9886,9958,10035,10106,10189,10264,10341,10423,10499,10607,10696,10778,10869,10965,11039,11120,11215,11269,11351,11417,11504,11590,11652,11716,11779,11848,11958,12071,12174,12281,12342,13904,14462,14547,14623", "endLines": "7,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,151,160,161,162", "endColumns": "12,76,76,81,96,91,96,131,82,77,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,132,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79,84,75,78", "endOffsets": "427,3234,3311,3393,3490,3582,4410,4542,4625,7044,7111,7204,7608,7671,7787,7850,7919,7978,8049,8108,8162,8283,8344,8407,8461,8534,8656,8744,8820,8911,8992,9075,9227,9313,9400,9533,9624,9707,9764,9815,9881,9953,10030,10101,10184,10259,10336,10418,10494,10602,10691,10773,10864,10960,11034,11115,11210,11264,11346,11412,11499,11585,11647,11711,11774,11843,11953,12066,12169,12276,12337,12392,13979,14542,14618,14697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b1798530a3a51669983781b25eadf6b2\\transformed\\browser-1.8.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,106", "endOffsets": "159,267,379,486"}, "to": {"startLines": "68,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6862,7209,7317,7429", "endColumns": "108,107,111,106", "endOffsets": "6966,7312,7424,7531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4174daf14328b785510b24290d95f19a\\transformed\\play-services-ads-24.3.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,254,304,364,431,501,604,668,819,952,1097,1147,1205,1315,1415,1459,1543,1578,1614,1667,1739,1783", "endColumns": "54,49,59,66,69,102,63,150,132,144,49,57,109,99,43,83,34,35,52,71,43,55", "endOffsets": "253,303,363,430,500,603,667,818,951,1096,1146,1204,1314,1414,1458,1542,1577,1613,1666,1738,1782,1838"}, "to": {"startLines": "135,136,137,140,141,142,143,144,145,146,147,148,149,150,152,153,154,155,156,157,158,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12397,12456,12510,12809,12880,12954,13061,13129,13284,13421,13570,13624,13686,13800,13984,14032,14120,14159,14199,14256,14332,14803", "endColumns": "58,53,63,70,73,106,67,154,136,148,53,61,113,103,47,87,38,39,56,75,47,59", "endOffsets": "12451,12505,12569,12875,12949,13056,13124,13279,13416,13565,13619,13681,13795,13899,14027,14115,14154,14194,14251,14327,14375,14858"}}]}]}